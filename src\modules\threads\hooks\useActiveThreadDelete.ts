/**
 * Hook để xử lý xóa active thread với integration
 * <PERSON><PERSON> gồm tìm thread tiếp theo, switch thread, và đồng bộ Redux
 */

import { useState, useCallback } from 'react';
import { useMutation, useQueryClient, InfiniteData } from '@tanstack/react-query';
import { useDispatch } from 'react-redux';
import { ThreadsService } from '../services/threads.service';
import { ThreadItem, GetThreadsResponse } from '@/shared/types/chat-streaming.types';
import { setCurrentThread, threadDeleted } from '@/shared/store/slices/threadIntegrationSlice';
import { THREADS_QUERY_KEYS } from '../constants';
import { markThreadAsDeleted } from '../services/deleted-thread-tracker.service';

interface UseActiveThreadDeleteProps {
  /**
   * Danh sách threads hiện có để tìm thread tiếp theo
   */
  availableThreads: ThreadItem[];
  
  /**
   * External chat stream để switch thread (optional)
   */
  externalChatStream?: {
    switchToThread?: (threadId: string) => Promise<void>;
    createNewThread?: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  } | undefined;
  
  /**
   * Callback khi xóa thành công
   */
  onThreadDeleted?: (deletedThreadId: string, nextThreadId: string | null) => void;
}

export const useActiveThreadDelete = ({ 
  availableThreads, 
  externalChatStream,
  onThreadDeleted 
}: UseActiveThreadDeleteProps) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [deletingThreadId, setDeletingThreadId] = useState<string | null>(null);

  // Mutation để delete active thread với integration
  const deleteActiveThreadMutation = useMutation({
    mutationFn: async (threadId: string) => {
      setDeletingThreadId(threadId);
      
      console.log('[useActiveThreadDelete] Deleting active thread with integration:', {
        threadId,
        availableThreadsCount: availableThreads.length
      });
      
      // Xóa thread từ API
      await ThreadsService.deleteThread(threadId);
      
      // Tìm thread tiếp theo để switch
      let nextThreadId: string | null = null;
      let isNewThreadCreated = false;
      
      const remainingThreads = availableThreads.filter(t => t.threadId !== threadId);
      
      console.log('[useActiveThreadDelete] Remaining threads after delete:', {
        remainingCount: remainingThreads.length,
        remainingThreads: remainingThreads.map(t => ({ id: t.threadId, name: t.name }))
      });
      
      if (remainingThreads.length > 0) {
        // Có threads khác, chọn thread mới nhất
        const sortedThreads = remainingThreads.sort((a, b) => b.updatedAt - a.updatedAt);
        nextThreadId = sortedThreads[0]?.threadId || null;
        
        console.log('[useActiveThreadDelete] Auto-switching to existing thread:', nextThreadId);
        
        // Auto-switch nếu có external chat stream
        if (externalChatStream && externalChatStream.switchToThread && nextThreadId) {
          try {
            await externalChatStream.switchToThread(nextThreadId);
          } catch (switchError) {
            console.error('[useActiveThreadDelete] Failed to switch to next thread:', switchError);
            nextThreadId = null;
          }
        }
      } else {
        // Không có thread nào còn lại, tạo thread mới
        console.log('[useActiveThreadDelete] No threads remaining, creating new thread...');
        
        if (externalChatStream && externalChatStream.createNewThread) {
          try {
            const newThread = await externalChatStream.createNewThread('New Chat');
            nextThreadId = newThread.threadId;
            isNewThreadCreated = true;

            console.log('[useActiveThreadDelete] Created new thread:', newThread);
          } catch (error) {
            console.error('[useActiveThreadDelete] Failed to create new thread:', error);
            nextThreadId = null;
          }
        } else {
          console.warn('[useActiveThreadDelete] No external chat stream available to create new thread');
        }
      }
      
      return { deletedThreadId: threadId, nextThreadId, isNewThreadCreated };
    },
    onSuccess: ({ deletedThreadId, nextThreadId, isNewThreadCreated }) => {
      console.log('[useActiveThreadDelete] Active thread deleted successfully:', {
        deletedThreadId,
        nextThreadId,
        isNewThreadCreated
      });

      // Mark thread as deleted để prevent future API calls
      markThreadAsDeleted(deletedThreadId);
      
      // Update Redux state IMMEDIATELY
      if (nextThreadId) {
        dispatch(setCurrentThread({
          threadId: nextThreadId,
          threadName: `Thread ${nextThreadId.slice(-8)}`, // Fallback name
          isNew: isNewThreadCreated || false
        }));
      } else {
        dispatch(setCurrentThread({
          threadId: null,
          threadName: null,
          isNew: false
        }));
      }
      
      // Dispatch thread deleted event để ChatPanel có thể listen
      dispatch(threadDeleted({
        threadId: deletedThreadId,
        nextThreadId: nextThreadId
      }));
      
      // Remove thread detail từ cache
      queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });

      // Optimistic update: Remove thread từ tất cả list queries ngay lập tức
      queryClient.setQueriesData(
        {
          queryKey: THREADS_QUERY_KEYS.ALL,
          predicate: (query) => query.queryKey.includes('list') || query.queryKey.includes('paginated')
        },
        (oldData: InfiniteData<GetThreadsResponse> | GetThreadsResponse | ThreadItem[] | unknown) => {
          if (!oldData) return oldData;

          // Handle infinite query data structure
          if ((oldData as InfiniteData<GetThreadsResponse>).pages) {
            const infiniteData = oldData as InfiniteData<GetThreadsResponse>;
            return {
              ...infiniteData,
              pages: infiniteData.pages.map((page: GetThreadsResponse) => ({
                ...page,
                items: page.items ? page.items.filter((thread: ThreadItem) => thread.threadId !== deletedThreadId) : []
              }))
            };
          }

          // Handle regular query data structure
          if (Array.isArray(oldData)) {
            const threadsArray = oldData as ThreadItem[];
            return threadsArray.filter((thread: ThreadItem) => thread.threadId !== deletedThreadId);
          }

          return oldData;
        }
      );

      // Invalidate queries để refetch fresh data (exclude detail queries để tránh 404)
      queryClient.invalidateQueries({
        queryKey: THREADS_QUERY_KEYS.ALL,
        predicate: (query) => {
          // Chỉ invalidate list/paginated queries, KHÔNG invalidate detail queries
          const isListQuery = query.queryKey.includes('list') || query.queryKey.includes('paginated');
          const isDetailQuery = query.queryKey.includes('detail');
          return isListQuery && !isDetailQuery;
        }
      });
      
      // Clear loading state
      setDeletingThreadId(null);
      
      // Trigger callback
      onThreadDeleted?.(deletedThreadId, nextThreadId);
    },
    onError: (error) => {
      console.error('[useActiveThreadDelete] Failed to delete active thread:', error);
      setDeletingThreadId(null);
    },
  });

  // Function để delete active thread
  const deleteActiveThread = useCallback((threadId: string) => {
    deleteActiveThreadMutation.mutate(threadId);
  }, [deleteActiveThreadMutation]);

  // Function để check xem thread có đang được xóa không
  const isThreadDeleting = useCallback((threadId: string) => {
    return deletingThreadId === threadId;
  }, [deletingThreadId]);

  return {
    // Actions
    deleteActiveThread,
    
    // State
    isThreadDeleting,
    isDeleting: deleteActiveThreadMutation.isPending,
    error: deleteActiveThreadMutation.error,
  };
};
